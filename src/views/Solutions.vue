<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "Solutions",
});

// 页面状态
const isLoaded = ref(false);

// 核心解决方案数据
const coreSolutions = ref([
  {
    icon: 'mdi:water-pump',
    category: '水务管理',
    title: '智慧水务综合管理平台',
    description: '集成供水、排水、污水处理全流程的智能化管理平台，实现水务运营的数字化转型',
    benefits: ['提升运营效率30%', '降低漏损率15%', '优化调度决策', '提高服务质量'],
    technologies: ['物联网', '大数据', 'GIS', 'AI算法'],
    projectCount: 120,
    clientCount: 50
  },
  {
    icon: 'mdi:weather-flood',
    category: '防汛预警',
    title: '智能防汛预警系统',
    description: '基于AI算法的洪水预警预报系统，提供精准的灾害预警和应急响应方案',
    benefits: ['预警提前24小时', '准确率提升40%', '减少灾害损失', '快速应急响应'],
    technologies: ['AI预测', '遥感监测', '气象分析', '数值模拟'],
    projectCount: 80,
    clientCount: 35
  },
  {
    icon: 'mdi:chart-multiple',
    category: '资源调度',
    title: '水资源优化调度平台',
    description: '基于大数据分析的水资源智能调配系统，实现区域水资源的精细化管理',
    benefits: ['节水效率提升25%', '调度精度提高', '成本降低20%', '环境效益显著'],
    technologies: ['优化算法', '数据分析', '调度模型', '智能控制'],
    projectCount: 95,
    clientCount: 42
  },
  {
    icon: 'mdi:monitor-dashboard',
    category: '智能监测',
    title: '水利工程安全监测',
    description: '覆盖大坝、闸站、堤防等水利工程的全方位安全监测预警系统',
    benefits: ['24小时监控', '预警及时准确', '运维成本降低', '安全系数提升'],
    technologies: ['传感网络', '结构分析', '安全评估', '预警算法'],
    projectCount: 150,
    clientCount: 60
  },
  {
    icon: 'mdi:leaf',
    category: '生态保护',
    title: '水生态环境监管平台',
    description: '水环境质量监测与生态保护综合管理平台，助力水生态文明建设',
    benefits: ['环境质量改善', '生态修复指导', '污染溯源追踪', '绿色发展支撑'],
    technologies: ['环境监测', '生态模型', '遥感分析', '数据融合'],
    projectCount: 70,
    clientCount: 28
  },
  {
    icon: 'mdi:city',
    category: '智慧城市',
    title: '城市水务数字孪生',
    description: '构建城市水务系统的数字孪生体，实现城市水务的智慧化管理',
    benefits: ['全域可视化', '精准仿真预测', '协同决策优化', '智慧运营管理'],
    technologies: ['数字孪生', '3D建模', '仿真计算', '虚实融合'],
    projectCount: 45,
    clientCount: 18
  }
]);

// 实施流程数据
const processSteps = ref([
  {
    icon: 'mdi:clipboard-text',
    title: '需求分析',
    description: '深入了解客户业务需求，制定详细的解决方案规划',
    details: ['业务调研分析', '技术可行性评估', '方案设计规划', '项目计划制定'],
    duration: '2-4周'
  },
  {
    icon: 'mdi:cog',
    title: '系统设计',
    description: '根据需求分析结果，设计系统架构和技术实现方案',
    details: ['系统架构设计', '数据库设计', '接口规范定义', '安全方案设计'],
    duration: '3-6周'
  },
  {
    icon: 'mdi:code-tags',
    title: '开发实施',
    description: '按照设计方案进行系统开发和功能实现',
    details: ['核心功能开发', '接口对接实现', '数据迁移处理', '系统集成测试'],
    duration: '8-16周'
  },
  {
    icon: 'mdi:test-tube',
    title: '测试验收',
    description: '全面测试系统功能，确保系统稳定可靠',
    details: ['功能测试验证', '性能压力测试', '安全漏洞检测', '用户验收测试'],
    duration: '2-4周'
  },
  {
    icon: 'mdi:rocket-launch',
    title: '上线部署',
    description: '系统正式上线运行，提供技术支持和培训',
    details: ['生产环境部署', '数据正式迁移', '用户培训指导', '试运行监控'],
    duration: '1-2周'
  },
  {
    icon: 'mdi:wrench',
    title: '运维支持',
    description: '提供持续的技术支持和系统维护服务',
    details: ['7×24小时监控', '故障快速响应', '系统优化升级', '技术支持服务'],
    duration: '长期'
  }
]);

// 成功案例数据
const successCases = ref([
  {
    title: '某省级水利厅智慧水利平台',
    category: '省级项目',
    description: '构建覆盖全省的智慧水利综合管理平台，实现水资源统一调度和智能化管理',
    image: '@/assets/images/case1.webp',
    achievements: [
      { value: '30%', label: '效率提升' },
      { value: '500+', label: '监测点' },
      { value: '24h', label: '实时监控' }
    ],
    technologies: ['大数据平台', 'AI算法', '物联网', 'GIS系统']
  },
  {
    title: '某市防汛指挥决策系统',
    category: '市级项目',
    description: '建设覆盖全市的防汛预警指挥系统，显著提升防汛应急响应能力',
    image: '@/assets/images/case2.webp',
    achievements: [
      { value: '6h', label: '预警提前' },
      { value: '95%', label: '准确率' },
      { value: '50%', label: '响应提速' }
    ],
    technologies: ['预警算法', '应急指挥', '移动应用', '视频监控']
  },
  {
    title: '某水库群智能调度系统',
    category: '工程项目',
    description: '构建多水库联合调度优化系统，实现水资源的精细化管理和优化配置',
    image: '@/assets/images/case3.webp',
    achievements: [
      { value: '20%', label: '节水效益' },
      { value: '15', label: '水库数量' },
      { value: '99%', label: '系统可用性' }
    ],
    technologies: ['调度算法', '数据融合', '优化模型', '智能控制']
  }
]);

// 事件处理
const handleSolutionDetail = (solution) => {
  console.log('查看解决方案详情:', solution);
};

const handleConsultation = () => {
  console.log('预约专家咨询');
};

const handleDemo = () => {
  console.log('观看演示视频');
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);
});
</script>

<template>
  <div class="solutions" :class="{ 'loaded': isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 15" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-network">
          <div v-for="i in 8" :key="i" :class="`network-node node-${i}`"></div>
          <svg class="network-lines" viewBox="0 0 100 100">
            <path class="network-path" d="M20,30 Q50,10 80,30" />
            <path class="network-path" d="M15,60 Q40,40 65,60" />
            <path class="network-path" d="M30,80 Q60,60 90,80" />
          </svg>
        </div>
      </div>
      
      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:lightbulb-on-outline" class="badge-icon" />
            <span>智慧水利数字化转型</span>
          </div>
          
          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">全方位解决方案</span>
            <span class="title-line title-highlight">赋能智慧水利</span>
          </h1>
          
          <p class="hero-subtitle fade-in-up delay-2">
            基于物联网、大数据、AI等前沿技术，提供从规划设计到运维管理的<br>
            <strong>全生命周期数字化解决方案</strong>
          </p>
          
          <div class="hero-features fade-in-up delay-3">
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:chart-timeline-variant" />
              </div>
              <div class="feature-text">
                <div class="feature-title">实时监控</div>
                <div class="feature-desc">7×24小时全天候监测</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:brain" />
              </div>
              <div class="feature-text">
                <div class="feature-title">智能分析</div>
                <div class="feature-desc">AI驱动的预测分析</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:shield-check" />
              </div>
              <div class="feature-text">
                <div class="feature-title">安全可靠</div>
                <div class="feature-desc">企业级安全保障</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="solution-hub">
              <div class="hub-center">
                <Icon icon="mdi:water-circle" class="hub-icon" />
                <div class="hub-pulse"></div>
              </div>
              <div class="hub-orbits">
                <div class="orbit orbit-1">
                  <div class="orbit-item item-1">
                    <Icon icon="mdi:chart-line" />
                  </div>
                </div>
                <div class="orbit orbit-2">
                  <div class="orbit-item item-2">
                    <Icon icon="mdi:cloud-outline" />
                  </div>
                  <div class="orbit-item item-3">
                    <Icon icon="mdi:shield-check" />
                  </div>
                </div>
                <div class="orbit orbit-3">
                  <div class="orbit-item item-4">
                    <Icon icon="mdi:trending-up" />
                  </div>
                  <div class="orbit-item item-5">
                    <Icon icon="mdi:database" />
                  </div>
                  <div class="orbit-item item-6">
                    <Icon icon="mdi:cog" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心解决方案 -->
    <section class="core-solutions">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:puzzle" />
            <span>核心方案</span>
          </div>
          <h2 class="section-title">专业解决方案矩阵</h2>
          <p class="section-subtitle">覆盖水利行业全业务场景的专业化解决方案</p>
        </div>
        
        <div class="solutions-grid">
          <div 
            v-for="(solution, index) in coreSolutions" 
            :key="index"
            class="solution-card"
            @click="handleSolutionDetail(solution)"
          >
            <div class="card-header">
              <div class="solution-icon">
                <Icon :icon="solution.icon" />
              </div>
              <div class="solution-category">{{ solution.category }}</div>
            </div>
            
            <div class="card-content">
              <h3 class="solution-title">{{ solution.title }}</h3>
              <p class="solution-description">{{ solution.description }}</p>
              
              <div class="solution-benefits">
                <div class="benefits-title">核心价值</div>
                <div class="benefits-list">
                  <div class="benefit" v-for="benefit in solution.benefits" :key="benefit">
                    <Icon icon="mdi:check-circle" class="benefit-icon" />
                    <span>{{ benefit }}</span>
                  </div>
                </div>
              </div>
              
              <div class="solution-tech">
                <div class="tech-title">技术栈</div>
                <div class="tech-tags">
                  <span class="tech-tag" v-for="tech in solution.technologies" :key="tech">{{ tech }}</span>
                </div>
              </div>
            </div>
            
            <div class="card-footer">
              <div class="solution-metrics">
                <div class="metric">
                  <span class="metric-number">{{ solution.projectCount }}+</span>
                  <span class="metric-label">成功案例</span>
                </div>
                <div class="metric">
                  <span class="metric-number">{{ solution.clientCount }}+</span>
                  <span class="metric-label">客户数量</span>
                </div>
              </div>
              <button class="explore-btn">
                <span>了解详情</span>
                <Icon icon="mdi:arrow-right" class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 实施流程 -->
    <section class="implementation-process">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:cogs" />
            <span>实施流程</span>
          </div>
          <h2 class="section-title">专业实施保障</h2>
          <p class="section-subtitle">标准化流程确保项目成功交付</p>
        </div>
        
        <div class="process-timeline">
          <div class="timeline-track"></div>
          <div 
            v-for="(step, index) in processSteps" 
            :key="index"
            class="process-step"
            :class="`step-${index + 1}`"
          >
            <div class="step-marker">
              <div class="marker-inner">
                <Icon :icon="step.icon" />
              </div>
              <div class="marker-number">{{ String(index + 1).padStart(2, '0') }}</div>
            </div>
            
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
              
              <div class="step-details">
                <div class="detail-item" v-for="detail in step.details" :key="detail">
                  <Icon icon="mdi:chevron-right" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>
              
              <div class="step-duration">
                <Icon icon="mdi:clock-outline" />
                <span>{{ step.duration }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 成功案例 -->
    <section class="success-cases">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:trophy" />
            <span>成功案例</span>
          </div>
          <h2 class="section-title">客户成功故事</h2>
          <p class="section-subtitle">真实案例见证解决方案价值</p>
        </div>
        
        <div class="cases-showcase">
          <div 
            v-for="(caseItem, index) in successCases" 
            :key="index"
            class="case-card"
          >
            <div class="case-visual">
              <div class="case-image">
                <img :src="caseItem.image" :alt="caseItem.title" />
              </div>
              <div class="case-overlay">
                <div class="case-category">{{ caseItem.category }}</div>
              </div>
            </div>
            
            <div class="case-content">
              <h3 class="case-title">{{ caseItem.title }}</h3>
              <p class="case-description">{{ caseItem.description }}</p>
              
              <div class="case-achievements">
                <div class="achievement" v-for="achievement in caseItem.achievements" :key="achievement.label">
                  <div class="achievement-number">{{ achievement.value }}</div>
                  <div class="achievement-label">{{ achievement.label }}</div>
                </div>
              </div>
              
              <div class="case-tech">
                <span class="tech-item" v-for="tech in caseItem.technologies" :key="tech">{{ tech }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 30" :key="i" class="grid-item"></div>
        </div>
      </div>
      
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:rocket-launch" />
              <span>开始您的数字化之旅</span>
            </div>
            <h2 class="cta-title">准备好迎接智慧水利的未来了吗？</h2>
            <p class="cta-subtitle">
              我们的专业团队将为您提供<strong>端到端的解决方案咨询</strong>和<strong>定制化实施服务</strong><br>
              让技术真正为您的业务创造价值
            </p>
            
            <div class="cta-benefits">
              <div class="benefit-item">
                <Icon icon="mdi:account-tie" />
                <span>专家1对1咨询</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:file-document-outline" />
                <span>免费方案评估</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:clock-fast" />
                <span>快速响应服务</span>
              </div>
            </div>
          </div>
          
          <div class="cta-actions">
            <button class="btn-primary large" @click="handleConsultation">
              <Icon icon="mdi:account-tie" class="btn-icon" />
              <span>预约专家咨询</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleDemo">
              <Icon icon="mdi:play-circle" class="btn-icon" />
              <span>观看演示视频</span>
            </button>
            
            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>************</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
.solutions-page {
  height: auto;
  overflow-y: auto;
  position: relative;
  color: #fff;

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url("@/assets/images/banner3.webp");
    background-size: cover;
    background-position: center;
    z-index: -1;
    filter: brightness(0.85) contrast(1.1) saturate(1.1);
    transition: all 1.2s ease;
    transform: scale(1.05);
  }

  &.is-loaded::before {
    transform: scale(1);
  }

  &::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.2)
    );
    z-index: -1;
  }

  .solutions-overview {
    padding: 150px 0 100px;
    position: relative;
    overflow: hidden;

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 40px;
      position: relative;
      z-index: 2;

      .section-title {
        font-size: 46px;
        text-align: center;
        margin: 0 0 60px;
        position: relative;
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);

        &.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .title-text {
          position: relative;
          display: inline-block;
          padding: 0 15px;

          &::before,
          &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 60px;
            height: 2px;
            background: linear-gradient(to right, transparent, #1e88e5);
          }

          &::before {
            right: 100%;
            background: linear-gradient(to right, #1e88e5, transparent);
          }

          &::after {
            left: 100%;
          }
        }
      }

      .overview-content {
        margin-bottom: 80px;
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;

        &.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .overview-description {
          font-size: 18px;
          line-height: 1.8;
          margin: 0 auto;
          text-align: justify;
          max-width: 90%;
          background-color: rgba(0, 0, 0, 0.2);
          padding: 35px;
          border-radius: 16px;
          border-left: 4px solid #1e88e5;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
      }

      .solution-list {
        .solution-item {
          opacity: 0;
          transform: translateY(40px);
          transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);

          &.visible {
            opacity: 1;
            transform: translateY(0);
          }

          &.pain-points {
            transition-delay: 0.2s;
          }

          &.advantages {
            transition-delay: 0.4s;
          }

          &.values {
            transition-delay: 0.6s;
          }

          &-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
            position: relative;

            .title-icon {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              background: linear-gradient(135deg, #1e88e5, #1565c0);
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 20px;
              box-shadow: 0 8px 20px rgba(21, 101, 192, 0.3);

              svg {
                font-size: 30px;
                color: white;
              }
            }

            .title-text {
              font-size: 36px;
              font-weight: bold;
              background: linear-gradient(to right, #fff, #90caf9);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              position: relative;

              &::after {
                content: "";
                position: absolute;
                bottom: -8px;
                left: 0;
                width: 100%;
                height: 2px;
                background: linear-gradient(to right, #1e88e5, transparent);
              }
            }
          }

          &-content {
            margin-bottom: 80px;

            .solution-item-column {
              display: grid;
              gap: 30px;

              &-item {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 16px;
                padding: 25px 30px;
                transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(5px);
                display: flex;
                align-items: center;

                &:hover {
                  background: rgba(255, 255, 255, 0.1);
                  transform: translateY(-5px);
                  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                  border-color: rgba(30, 136, 229, 0.3);
                }

                .item-number {
                  font-size: 40px;
                  font-weight: bold;
                  color: #1e88e5;
                  margin-right: 25px;
                  line-height: 1;
                  text-shadow: 0 0 15px rgba(30, 136, 229, 0.4);
                  min-width: 70px;
                }

                .item-text {
                  font-size: 18px;
                  line-height: 1.6;
                }

                .item-icon {
                  width: 50px;
                  height: 50px;
                  border-radius: 12px;
                  background: linear-gradient(
                    135deg,
                    rgba(30, 136, 229, 0.2),
                    rgba(21, 101, 192, 0.2)
                  );
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 20px;
                  transition: all 0.4s ease;

                  svg {
                    font-size: 28px;
                    color: #64b5f6;
                    transition: all 0.4s ease;
                  }
                }

                &:hover .item-icon {
                  background: linear-gradient(
                    135deg,
                    rgba(30, 136, 229, 0.3),
                    rgba(21, 101, 192, 0.3)
                  );
                  transform: rotate(10deg);

                  svg {
                    color: #90caf9;
                    transform: scale(1.1);
                  }
                }

                .item-content {
                  flex: 1;

                  .item-header {
                    font-size: 20px;
                    font-weight: bold;
                    color: #90caf9;
                    margin-bottom: 8px;
                  }

                  .item-desc {
                    font-size: 16px;
                    color: rgba(255, 255, 255, 0.8);
                  }
                }
              }
            }
          }

          // 行业痛点样式
          &.pain-points {
            .solution-item-column {
              grid-template-columns: repeat(2, 1fr);
            }
          }

          // 方案优势样式
          &.advantages {
            .solution-item-column {
              grid-template-columns: repeat(2, 1fr);
            }
          }

          // 客户价值样式
          &.values {
            .solution-item-column {
              grid-template-columns: repeat(3, 1fr);
            }
          }
        }
      }
    }
  }

  // 装饰元素
  .decoration-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;

    .deco-circle {
      position: absolute;
      border-radius: 50%;
      border: 1px solid rgba(30, 136, 229, 0.1);

      &.c1 {
        width: 300px;
        height: 300px;
        top: 10%;
        left: -150px;
      }

      &.c2 {
        width: 500px;
        height: 500px;
        bottom: 10%;
        right: -250px;
        border-width: 2px;
        border-color: rgba(30, 136, 229, 0.05);
      }

      &.c3 {
        width: 200px;
        height: 200px;
        top: 40%;
        right: 10%;
      }
    }

    .deco-line {
      position: absolute;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(30, 136, 229, 0.1),
        transparent
      );
      height: 1px;

      &.l1 {
        width: 100%;
        top: 30%;
        left: 0;
        transform: rotate(-5deg);
      }

      &.l2 {
        width: 80%;
        bottom: 20%;
        right: 0;
        transform: rotate(3deg);
      }
    }
  }
}

// 媒体查询
@media (max-width: 1200px) {
  .solutions-page {
    .solutions-overview {
      .container {
        .solution-list {
          .solution-item {
            &.advantages .solution-item-column,
            &.pain-points .solution-item-column {
              grid-template-columns: 1fr;
            }

            &.values .solution-item-column {
              grid-template-columns: repeat(2, 1fr);
            }

            &-title {
              margin-bottom: 30px;

              .title-text {
                font-size: 32px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .solutions-page {
    .solutions-overview {
      padding: 120px 0 80px;

      .container {
        padding: 0 20px;

        .section-title {
          font-size: 36px;
          margin-bottom: 40px;
        }

        .overview-content {
          margin-bottom: 60px;

          .overview-description {
            font-size: 16px;
            padding: 25px;
            max-width: 100%;
          }
        }

        .solution-list {
          .solution-item {
            &.values .solution-item-column {
              grid-template-columns: 1fr;
            }

            &-title {
              .title-icon {
                width: 50px;
                height: 50px;

                svg {
                  font-size: 24px;
                }
              }

              .title-text {
                font-size: 28px;
              }
            }

            &-content {
              margin-bottom: 60px;

              .solution-item-column-item {
                padding: 20px;

                .item-number {
                  font-size: 32px;
                  margin-right: 15px;
                  min-width: 50px;
                }

                .item-text {
                  font-size: 16px;
                }

                .item-content {
                  .item-header {
                    font-size: 18px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .decoration-elements {
      display: none;
    }
  }
}
</style>
