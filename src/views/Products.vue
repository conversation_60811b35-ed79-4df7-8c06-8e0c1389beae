<template>
  <div class="products" :class="{ 'loaded': isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 12" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:package-variant-closed" class="badge-icon" />
            <span>企业级智慧水利产品矩阵</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">全方位数字化</span>
            <span class="title-line title-highlight">水利产品生态</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            融合IoT、大数据、AI、云计算等前沿技术<br>
            <strong>构建完整的智慧水利产品体系，赋能行业数字化转型</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:package-variant" />
              </div>
              <div class="stat-number" data-count="15">0</div>
              <div class="stat-label">核心产品</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:chart-line" />
              </div>
              <div class="stat-number" data-count="800">0</div>
              <div class="stat-label">成功案例</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:star" />
              </div>
              <div class="stat-number" data-count="99">0</div>
              <div class="stat-label">满意度%</div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="central-hub">
              <div class="hub-core">
                <Icon icon="mdi:water-circle" class="core-icon" />
                <div class="core-pulse"></div>
              </div>
              <div class="hub-ring ring-outer"></div>
              <div class="hub-ring ring-middle"></div>
              <div class="hub-ring ring-inner"></div>
            </div>

            <div class="product-constellation">
              <div class="constellation-node node-1">
                <div class="node-icon">
                  <Icon icon="mdi:chart-line" />
                </div>
                <div class="node-label">数据分析</div>
              </div>
              <div class="constellation-node node-2">
                <div class="node-icon">
                  <Icon icon="mdi:shield-check" />
                </div>
                <div class="node-label">安全监控</div>
              </div>
              <div class="constellation-node node-3">
                <div class="node-icon">
                  <Icon icon="mdi:cloud-outline" />
                </div>
                <div class="node-label">云端服务</div>
              </div>
              <div class="constellation-node node-4">
                <div class="node-icon">
                  <Icon icon="mdi:trending-up" />
                </div>
                <div class="node-label">智能预测</div>
              </div>
              <div class="constellation-node node-5">
                <div class="node-icon">
                  <Icon icon="mdi:water-pump" />
                </div>
                <div class="node-label">设备管理</div>
              </div>
              <div class="constellation-node node-6">
                <div class="node-icon">
                  <Icon icon="mdi:chart-multiple" />
                </div>
                <div class="node-label">综合调度</div>
              </div>
            </div>

            <div class="connection-web">
              <svg class="connection-svg" viewBox="0 0 400 400">
                <g class="connections">
                  <path class="connection-path" d="M200,200 L120,80" />
                  <path class="connection-path" d="M200,200 L320,100" />
                  <path class="connection-path" d="M200,200 L350,240" />
                  <path class="connection-path" d="M200,200 L280,320" />
                  <path class="connection-path" d="M200,200 L120,320" />
                  <path class="connection-path" d="M200,200 L60,180" />
                </g>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品分类导航 -->
    <section class="categories-section">
      <div class="container">
        <div class="categories-header">
          <h2 class="categories-title">产品分类</h2>
          <p class="categories-subtitle">专业化产品线，满足不同场景需求</p>
        </div>

        <div class="categories-grid">
          <button
            v-for="category in categories"
            :key="category.id"
            :class="['category-card', { 'active': activeCategory === category.id }]"
            @click="setActiveCategory(category.id)"
          >
            <div class="category-visual">
              <div class="category-icon">
                <Icon :icon="category.icon" />
              </div>
              <div class="category-bg"></div>
            </div>
            <div class="category-content">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-desc">{{ category.description }}</p>
              <div class="category-count">{{ category.count }}+ 产品</div>
            </div>
            <div class="category-indicator">
              <Icon icon="mdi:arrow-right" />
            </div>
          </button>
        </div>
      </div>
    </section>

    <!-- 产品展示区域 -->
    <section class="products-showcase">
      <div class="container">
        <div class="showcase-header">
          <h2 class="showcase-title">
            {{ categories.find(c => c.id === activeCategory)?.name || '全部产品' }}
          </h2>
          <div class="showcase-meta">
            <span class="product-count">{{ filteredProducts.length }} 个产品</span>
            <div class="view-toggle">
              <button
                :class="['toggle-btn', { 'active': viewMode === 'grid' }]"
                @click="viewMode = 'grid'"
              >
                <Icon icon="mdi:view-grid" />
              </button>
              <button
                :class="['toggle-btn', { 'active': viewMode === 'list' }]"
                @click="viewMode = 'list'"
              >
                <Icon icon="mdi:view-list" />
              </button>
            </div>
          </div>
        </div>

        <div :class="['products-grid', viewMode]">
          <div
            v-for="product in filteredProducts"
            :key="product.id"
            class="product-card"
            @click="handleProductDetail(product)"
          >
            <div class="card-visual">
              <div class="product-icon">
                <Icon :icon="product.icon" />
              </div>
              <div class="product-badge" :class="product.priority">
                {{ product.badge }}
              </div>
              <div class="card-gradient"></div>
            </div>

            <div class="card-content">
              <div class="product-header">
                <h3 class="product-title">{{ product.title }}</h3>
                <div class="product-status" :class="product.status.toLowerCase()">
                  <Icon :icon="product.statusIcon" class="status-icon" />
                  <span>{{ product.status }}</span>
                </div>
              </div>

              <p class="product-description">{{ product.description }}</p>

              <div class="product-features">
                <div class="features-title">核心功能</div>
                <div class="features-list">
                  <div class="feature" v-for="feature in product.features.slice(0, 3)" :key="feature">
                    <Icon icon="mdi:check-circle" class="feature-icon" />
                    <span>{{ feature }}</span>
                  </div>
                  <div v-if="product.features.length > 3" class="feature-more">
                    +{{ product.features.length - 3 }} 更多功能
                  </div>
                </div>
              </div>

              <div class="product-tags">
                <span class="tag" v-for="tag in product.tags.slice(0, 3)" :key="tag">{{ tag }}</span>
                <span v-if="product.tags.length > 3" class="tag-more">+{{ product.tags.length - 3 }}</span>
              </div>
            </div>

            <div class="card-footer">
              <div class="product-metrics">
                <div class="metric">
                  <Icon icon="mdi:eye" />
                  <span>{{ product.views || '1.2k' }}</span>
                </div>
                <div class="metric">
                  <Icon icon="mdi:star" />
                  <span>{{ product.rating || '4.8' }}</span>
                </div>
              </div>
              <button class="detail-btn">
                <span>查看详情</span>
                <Icon icon="mdi:arrow-right" class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredProducts.length === 0" class="empty-state">
          <div class="empty-visual">
            <Icon icon="mdi:package-variant" class="empty-icon" />
            <div class="empty-animation">
              <div class="animation-dot"></div>
              <div class="animation-dot"></div>
              <div class="animation-dot"></div>
            </div>
          </div>
          <h3 class="empty-title">该分类下暂无产品</h3>
          <p class="empty-description">我们正在不断完善产品线，敬请期待更多创新产品</p>
          <button class="empty-action" @click="setActiveCategory('all')">
            <span>查看全部产品</span>
            <Icon icon="mdi:arrow-right" />
          </button>
        </div>
      </div>
    </section>

    <!-- 产品优势 -->
    <section class="advantages-section">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:star-four-points" />
            <span>核心优势</span>
          </div>
          <h2 class="section-title">为什么选择我们的产品</h2>
          <p class="section-subtitle">专业技术 × 创新理念 × 服务保障 = 卓越产品体验</p>
        </div>

        <div class="advantages-grid">
          <div class="advantage-card" v-for="(advantage, index) in advantages" :key="index">
            <div class="advantage-visual">
              <div class="advantage-number">{{ String(index + 1).padStart(2, '0') }}</div>
              <div class="advantage-icon">
                <Icon :icon="advantage.icon" />
              </div>
              <div class="visual-bg"></div>
            </div>
            <div class="advantage-content">
              <h3 class="advantage-title">{{ advantage.title }}</h3>
              <p class="advantage-description">{{ advantage.description }}</p>
              <div class="advantage-details">
                <div class="detail" v-for="detail in advantage.details" :key="detail">
                  <Icon icon="mdi:arrow-right-circle" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>
            </div>
            <div class="advantage-decorator"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-pattern">
          <div v-for="i in 50" :key="i" class="pattern-dot"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:handshake" />
              <span>专业服务</span>
            </div>
            <h2 class="cta-title">找到适合您的解决方案了吗？</h2>
            <p class="cta-subtitle">
              我们的技术专家团队将为您提供<strong>个性化的产品咨询</strong>和<strong>定制化解决方案</strong><br>
              助力您的数字化转型之路
            </p>
            <div class="cta-features">
              <div class="cta-feature">
                <Icon icon="mdi:account-tie" />
                <span>1对1专家咨询</span>
              </div>
              <div class="cta-feature">
                <Icon icon="mdi:clock-fast" />
                <span>24小时快速响应</span>
              </div>
              <div class="cta-feature">
                <Icon icon="mdi:shield-check" />
                <span>方案质量保证</span>
              </div>
            </div>
          </div>
          <div class="cta-actions">
            <button class="btn-primary large" @click="handleConsultation">
              <Icon icon="mdi:account-tie" class="btn-icon" />
              <span>免费产品咨询</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleDemo">
              <Icon icon="mdi:play-circle" class="btn-icon" />
              <span>申请产品演示</span>
            </button>
            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>************</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { Icon } from "@iconify/vue";
import { ref, computed, onMounted } from "vue";

defineOptions({
  name: "Products",
});

// 页面状态
const isLoaded = ref(false);
const activeCategory = ref('all');
const viewMode = ref('grid');

// 产品分类数据
const categories = ref([
  {
    id: 'all',
    name: '全部产品',
    icon: 'mdi:view-grid',
    description: '完整产品矩阵',
    count: 15
  },
  {
    id: 'monitoring',
    name: '智能监测',
    icon: 'mdi:monitor-dashboard',
    description: '实时监控系统',
    count: 5
  },
  {
    id: 'analysis',
    name: '数据分析',
    icon: 'mdi:chart-line',
    description: '大数据分析平台',
    count: 4
  },
  {
    id: 'management',
    name: '资源管理',
    icon: 'mdi:water-pump',
    description: '水资源调配',
    count: 3
  },
  {
    id: 'warning',
    name: '预警系统',
    icon: 'mdi:weather-flood',
    description: '防汛预警',
    count: 3
  }
]);

// 产品数据
const allProducts = ref([
  {
    id: 1,
    title: '智慧水务综合管理平台',
    description: '集成物联网传感器、大数据分析、AI预测的一体化水务管理解决方案，实现水务全流程数字化管理',
    icon: 'mdi:water-pump',
    category: 'management',
    priority: 'high',
    badge: '核心产品',
    status: '运行中',
    statusIcon: 'mdi:check-circle',
    features: ['实时监控', '智能调度', '预测分析', '自动报警', '移动端管理', '数据可视化'],
    tags: ['物联网', '大数据', 'AI算法', '云计算', '移动应用'],
    views: '2.1k',
    rating: '4.9'
  },
  {
    id: 2,
    title: '防汛四预智能系统',
    description: '通过预报、预警、预演、预案的全流程闭环管理，基于AI算法提供精准的防汛决策支持',
    icon: 'mdi:weather-flood',
    category: 'warning',
    priority: 'urgent',
    badge: '重点推荐',
    status: '最新版',
    statusIcon: 'mdi:star-circle',
    features: ['智能预报', '精准预警', '3D预演', '应急预案', '风险评估', '决策支持'],
    tags: ['AI预警', '应急响应', '风险评估', '决策支持'],
    views: '1.8k',
    rating: '4.8'
  },
  {
    id: 3,
    title: '水资源优化配置平台',
    description: '基于大数据分析和优化算法的水资源智能配置系统，提高水资源利用效率',
    icon: 'mdi:chart-multiple',
    category: 'analysis',
    priority: 'high',
    badge: '智能优化',
    status: '稳定版',
    statusIcon: 'mdi:check-circle',
    features: ['需求预测', '配置优化', '调度管理', '效益分析', '节水评估'],
    tags: ['优化算法', '资源配置', '节水技术', '效益分析'],
    views: '1.5k',
    rating: '4.7'
  },
  {
    id: 4,
    title: '河湖水质监测系统',
    description: '集成多参数水质传感器的实时监测网络，提供全方位的水环境监控服务',
    icon: 'mdi:monitor-dashboard',
    category: 'monitoring',
    priority: 'medium',
    badge: '环保认证',
    status: '运行中',
    statusIcon: 'mdi:check-circle',
    features: ['多参数监测', '实时传输', '超标报警', '趋势分析', '报表生成'],
    tags: ['水质监测', '传感器', '实时监控', '数据分析'],
    views: '1.3k',
    rating: '4.6'
  },
  {
    id: 5,
    title: '水利工程安全监测平台',
    description: '针对大坝、堤防等水利工程的安全监测系统，确保工程运行安全',
    icon: 'mdi:shield-check',
    category: 'monitoring',
    priority: 'high',
    badge: '安全保障',
    status: '运行中',
    statusIcon: 'mdi:check-circle',
    features: ['结构监测', '变形分析', '安全评估', '风险预警', '应急响应'],
    tags: ['安全监测', '结构分析', '风险评估', '预警系统'],
    views: '1.1k',
    rating: '4.8'
  },
  {
    id: 6,
    title: '智能灌溉控制系统',
    description: '基于土壤墒情和气象数据的精准灌溉系统，实现农业用水的智能化管理',
    icon: 'mdi:sprinkler-variant',
    category: 'management',
    priority: 'medium',
    badge: '节水技术',
    status: '推广中',
    statusIcon: 'mdi:trending-up',
    features: ['土壤监测', '智能控制', '节水优化', '作物管理', '数据分析'],
    tags: ['精准农业', '节水灌溉', '智能控制', '农业物联网'],
    views: '950',
    rating: '4.5'
  },
  {
    id: 7,
    title: '水利大数据分析平台',
    description: '集成多源数据的综合分析平台，提供深度数据挖掘和智能决策支持',
    icon: 'mdi:chart-line',
    category: 'analysis',
    priority: 'high',
    badge: '数据驱动',
    status: '最新版',
    statusIcon: 'mdi:star-circle',
    features: ['数据挖掘', '机器学习', '预测建模', '可视化展示', '报告生成'],
    tags: ['大数据', '机器学习', '数据挖掘', '预测分析'],
    views: '1.7k',
    rating: '4.9'
  },
  {
    id: 8,
    title: '洪水预警预报系统',
    description: '基于气象水文数据的洪水预警系统，提供及时准确的洪水预报预警信息',
    icon: 'mdi:flood',
    category: 'warning',
    priority: 'urgent',
    badge: '应急必备',
    status: '运行中',
    statusIcon: 'mdi:check-circle',
    features: ['洪水预报', '风险评估', '预警发布', '应急响应', '历史分析'],
    tags: ['洪水预警', '气象分析', '风险评估', '应急管理'],
    views: '1.4k',
    rating: '4.7'
  }
]);

// 优势数据
const advantages = ref([
  {
    icon: 'mdi:rocket-launch',
    title: '技术领先',
    description: '采用最新的物联网、AI、大数据等前沿技术',
    details: ['自主研发核心算法', '持续技术创新', '专利技术保护', '技术标准制定参与']
  },
  {
    icon: 'mdi:shield-check-outline',
    title: '质量可靠',
    description: '严格的质量管理体系确保产品稳定可靠',
    details: ['ISO质量认证', '多重测试验证', '7×24小时监控', '快速故障响应']
  },
  {
    icon: 'mdi:account-group',
    title: '专业服务',
    description: '资深专家团队提供全方位专业服务',
    details: ['15年行业经验', '专业技术团队', '一对一服务', '定制化解决方案']
  },
  {
    icon: 'mdi:trending-up',
    title: '效果显著',
    description: '帮助客户实现显著的效率提升和成本节约',
    details: ['效率提升30%+', '成本降低20%+', '决策准确率95%+', '客户满意度99%+']
  }
]);

// 计算属性 - 过滤产品
const filteredProducts = computed(() => {
  if (activeCategory.value === 'all') {
    return allProducts.value;
  }
  return allProducts.value.filter(
    (product) => product.category === activeCategory.value
  );
});

// 方法
const setActiveCategory = (categoryId) => {
  activeCategory.value = categoryId;
};

const handleProductDetail = (product) => {
  console.log('查看产品详情:', product);
  // 这里可以跳转到产品详情页面或打开模态框
};

const handleConsultation = () => {
  console.log('申请产品咨询');
  // 这里可以打开咨询表单
};

const handleDemo = () => {
  console.log('申请产品演示');
  // 这里可以打开演示申请表单
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);

  // 数字动画效果
  const animateNumbers = () => {
    const numbers = document.querySelectorAll('.stat-number[data-count]');
    numbers.forEach(number => {
      const target = parseInt(number.getAttribute('data-count'));
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        number.textContent = Math.floor(current);
      }, 50);
    });
  };

  setTimeout(animateNumbers, 500);
});
</script>
<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.products {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 { animation-delay: 0.1s; }
      &.delay-2 { animation-delay: 0.2s; }
      &.delay-3 { animation-delay: 0.3s; }
      &.delay-4 { animation-delay: 0.4s; }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("@/assets/images/banner2.webp");
      background-size: cover;
      background-position: center;
      opacity: 0.2;
      mix-blend-mode: overlay;
    }
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-80) 0%,
      var(--corporate-blue) 50%,
      var(--midnight-blue) 100%
    );
  }

  .hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .particle {
      position: absolute;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.3;

      &.particle-1 { width: 4px; height: 4px; top: 15%; left: 20%; @include floating-animation(3.2s, 12px, 0s); }
      &.particle-2 { width: 6px; height: 6px; top: 25%; left: 80%; @include floating-animation(4.1s, 18px, 0.5s); }
      &.particle-3 { width: 3px; height: 3px; top: 45%; left: 10%; @include floating-animation(3.8s, 15px, 1s); }
      &.particle-4 { width: 7px; height: 7px; top: 65%; left: 70%; @include floating-animation(4.5s, 22px, 1.5s); }
      &.particle-5 { width: 5px; height: 5px; top: 80%; left: 30%; @include floating-animation(3.6s, 14px, 2s); }
      &.particle-6 { width: 4px; height: 4px; top: 35%; left: 85%; @include floating-animation(4.2s, 16px, 2.5s); }
      &.particle-7 { width: 6px; height: 6px; top: 55%; left: 15%; @include floating-animation(3.9s, 20px, 0.8s); }
      &.particle-8 { width: 3px; height: 3px; top: 75%; left: 60%; @include floating-animation(4.3s, 11px, 1.8s); }
      &.particle-9 { width: 5px; height: 5px; top: 20%; left: 45%; @include floating-animation(3.7s, 17px, 2.2s); }
      &.particle-10 { width: 7px; height: 7px; top: 40%; left: 75%; @include floating-animation(4.0s, 19px, 1.2s); }
      &.particle-11 { width: 4px; height: 4px; top: 60%; left: 25%; @include floating-animation(4.4s, 13px, 0.3s); }
      &.particle-12 { width: 6px; height: 6px; top: 85%; left: 55%; @include floating-animation(3.5s, 21px, 2.8s); }
    }
  }

  .hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--primary-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--primary-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);
  }

  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-alpha-20);
    border: 1px solid var(--primary-alpha-40);
    border-radius: var(--radius-2xl);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-8);
    @include glass-morphism(0.15, 16px, 0.3);

    .badge-icon {
      color: var(--electric-blue);
      font-size: var(--text-lg);
    }
  }

  .hero-title {
    font-size: var(--text-6xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-8);

    .title-line {
      display: block;
      margin-bottom: var(--space-2);
    }

    .title-highlight {
      @include gradient-text(var(--gradient-neon));
    }

    @media (max-width: 768px) {
      font-size: var(--text-4xl);
    }
  }

  .hero-subtitle {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--sky-blue);
    margin-bottom: var(--space-10);

    strong {
      color: var(--white);
      @include gradient-text(var(--gradient-innovation));
    }
  }

  .hero-stats {
    display: flex;
    align-items: center;
    gap: var(--space-8);

    @media (max-width: 640px) {
      flex-direction: column;
      gap: var(--space-6);
    }

    .stat-item {
      text-align: center;

      .stat-icon {
        font-size: var(--text-2xl);
        color: var(--electric-blue);
        margin-bottom: var(--space-2);
      }

      .stat-number {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        color: var(--electric-blue);
        margin-bottom: var(--space-1);
        text-shadow: 0 0 10px var(--electric-blue);
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
      }
    }

    .stat-divider {
      width: 1px;
      height: 60px;
      background: var(--primary-alpha-40);

      @media (max-width: 640px) {
        width: 60px;
        height: 1px;
      }
    }
  }

  // 视觉区域
  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;

    @media (max-width: 968px) {
      height: 400px;
      order: -1;
    }
  }

  .visual-container {
    position: relative;
    width: 500px;
    height: 500px;

    @media (max-width: 968px) {
      width: 350px;
      height: 350px;
    }
  }

  .central-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .hub-core {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;

      .core-icon {
        font-size: 60px;
        color: var(--electric-blue);
        z-index: 3;
        position: relative;
        @include floating-animation(3s, 8px);
        filter: drop-shadow(0 0 30px var(--electric-blue));
      }

      .core-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(circle, var(--electric-blue) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0.3;
        animation: pulse 2s infinite;
      }
    }

    .hub-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid;
      border-radius: 50%;
      opacity: 0.6;

      &.ring-inner {
        width: 160px;
        height: 160px;
        border-color: var(--tech-alpha-50);
        animation: rotate 15s linear infinite;
      }

      &.ring-middle {
        width: 240px;
        height: 240px;
        border-color: var(--primary-alpha-30);
        animation: rotate 25s linear infinite reverse;
      }

      &.ring-outer {
        width: 320px;
        height: 320px;
        border-color: var(--electric-blue);
        opacity: 0.3;
        animation: rotate 35s linear infinite;
      }
    }
  }

  .product-constellation {
    .constellation-node {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);

      .node-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-glass);
        @include glass-morphism(0.2, 12px, 0.3);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--electric-blue);
        font-size: var(--text-xl);
        transition: var(--transition-all);

        &:hover {
          transform: scale(1.1);
          box-shadow: var(--glow-neon);
        }
      }

      .node-label {
        font-size: var(--text-xs);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
        text-align: center;
        white-space: nowrap;
      }

      &.node-1 {
        top: 15%;
        left: 25%;
        @include floating-animation(4s, 12px, 0s);
      }

      &.node-2 {
        top: 20%;
        right: 20%;
        @include floating-animation(3.5s, 10px, 0.5s);
      }

      &.node-3 {
        right: 10%;
        top: 50%;
        @include floating-animation(4.2s, 15px, 1s);
      }

      &.node-4 {
        bottom: 20%;
        right: 25%;
        @include floating-animation(3.8s, 11px, 1.5s);
      }

      &.node-5 {
        bottom: 15%;
        left: 20%;
        @include floating-animation(4.1s, 13px, 2s);
      }

      &.node-6 {
        left: 10%;
        top: 45%;
        @include floating-animation(3.7s, 9px, 2.5s);
      }
    }
  }

  .connection-web {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .connection-svg {
      width: 100%;
      height: 100%;

      .connection-path {
        stroke: var(--electric-blue);
        stroke-width: 1;
        stroke-dasharray: 5,5;
        opacity: 0.4;
        animation: dash 3s linear infinite;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.1; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes rotate {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes dash {
  to { stroke-dashoffset: -20; }
}

// 产品展示区
.products-showcase {
  @include section-padding(var(--space-20), var(--space-20));

  // 分类导航
  .category-nav {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    margin-bottom: var(--space-16);
    flex-wrap: wrap;

    .category-btn {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-6);
      background: var(--white);
      color: var(--gray-600);
      border: 2px solid var(--gray-200);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: var(--transition-all);

      .category-icon {
        font-size: var(--text-lg);
      }

      &:hover {
        border-color: var(--primary-blue);
        color: var(--primary-blue);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      &.active {
        background: var(--gradient-primary);
        color: var(--white);
        border-color: var(--primary-blue);
        box-shadow: var(--shadow-lg);

        .category-icon {
          color: var(--white);
        }
      }
    }
  }

  // 产品网格
  .products-grid {
    @include responsive-grid(auto-fit, 380px, var(--space-8));
    margin-bottom: var(--space-16);
  }

  .product-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    border: 1px solid var(--gray-200);

    @include card-hover(1.03, -8px);

    .product-visual {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;
      overflow: hidden;

      .product-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .product-badges {
        position: absolute;
        top: var(--space-4);
        right: var(--space-4);
        display: flex;
        flex-direction: column;
        gap: var(--space-2);

        .badge {
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);

          &.new {
            background: var(--gradient-innovation);
            color: var(--white);
          }

          &.hot {
            background: var(--warning-orange);
            color: var(--white);
          }

          &.featured {
            background: var(--gradient-neon);
            color: var(--white);
          }
        }
      }

      .hover-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--primary-alpha-80);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: var(--transition-all);

        .overlay-icon {
          font-size: var(--text-4xl);
          color: var(--white);
          transform: scale(0.8);
          transition: var(--transition-all);
        }
      }
    }

    &:hover {
      .product-visual {
        .product-icon {
          transform: scale(1.1) rotateY(10deg);
        }

        .hover-overlay {
          opacity: 1;

          .overlay-icon {
            transform: scale(1);
          }
        }
      }
    }

    .product-content {
      padding: var(--space-6);

      .product-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        line-height: var(--leading-tight);
      }

      .product-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
        font-size: var(--text-sm);
      }

      .product-features {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        margin-bottom: var(--space-4);

        .feature {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: var(--gray-700);

          .feature-icon {
            color: var(--success-green);
            font-size: var(--text-base);
            flex-shrink: 0;
          }
        }
      }

      .product-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);
        margin-bottom: var(--space-6);

        .tag {
          background: var(--crystal-blue);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }
      }

      .product-actions {
        display: flex;
        gap: var(--space-3);

        button {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          padding: var(--space-2-5) var(--space-4);
          border-radius: var(--radius-lg);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          cursor: pointer;
          transition: var(--transition-all);
          flex: 1;
          justify-content: center;

          &.btn-primary {
            background: var(--gradient-primary);
            color: var(--white);
            border: none;

            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-hover);
            }
          }

          &.btn-secondary {
            background: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);

            &:hover {
              background: var(--primary-blue);
              color: var(--white);
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  // 加载更多
  .load-more {
    text-align: center;

    .btn-outline {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-4) var(--space-8);
      background: transparent;
      color: var(--primary-blue);
      border: 2px solid var(--primary-blue);
      border-radius: var(--radius-2xl);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: var(--transition-all);

      &:hover {
        background: var(--primary-blue);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
      }
    }
  }
}

// 技术优势
.tech-advantages {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .advantages-grid {
    @include responsive-grid(auto-fit, 280px, var(--space-8));
  }

  .advantage-item {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -4px);

    .advantage-visual {
      position: relative;
      margin-bottom: var(--space-6);

      .advantage-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        box-shadow: var(--glow-primary);
        position: relative;
        z-index: 2;
      }

      .advantage-bg {
        position: absolute;
        top: -20px;
        right: -20px;
        width: 100px;
        height: 100px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: 1;
      }
    }

    .advantage-content {
      h3 {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
      }

      p {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
      }

      ul {
        list-style: none;

        li {
          position: relative;
          padding-left: var(--space-4);
          margin-bottom: var(--space-2);
          color: var(--gray-700);
          font-size: var(--text-sm);

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: var(--primary-blue);
            border-radius: 50%;
          }
        }
      }
    }
  }
}

// CTA 区域
.products-cta {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-primary);
  color: var(--white);

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }
  }

  .cta-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-4);
  }

  .cta-subtitle {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    opacity: 0.9;
  }

  .cta-actions {
    display: flex;
    gap: var(--space-4);

    @media (max-width: 640px) {
      flex-direction: column;
    }

    button {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-4) var(--space-6);
      border-radius: var(--radius-2xl);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: var(--transition-all);
      border: none;
      white-space: nowrap;

      &.large {
        padding: var(--space-5) var(--space-8);
        font-size: var(--text-lg);
      }

      &.btn-primary {
        background: var(--white);
        color: var(--primary-blue);

        &:hover {
          background: var(--crystal-blue);
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }
      }

      &.btn-secondary {
        background: transparent;
        color: var(--white);
        border: 2px solid var(--white);

        &:hover {
          background: var(--white);
          color: var(--primary-blue);
          transform: translateY(-2px);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .page-header {
    min-height: 50vh;

    .page-title {
      font-size: var(--text-4xl);
    }

    .page-subtitle {
      font-size: var(--text-lg);
    }
  }

  .products-showcase {
    .category-nav {
      .category-btn {
        font-size: var(--text-xs);
        padding: var(--space-2) var(--space-4);
      }
    }
  }
}

@media (max-width: 640px) {
  .page-header {
    .page-title {
      font-size: var(--text-3xl);
    }

    .page-subtitle {
      font-size: var(--text-base);
    }
  }

  .product-card {
    .product-content {
      .product-actions {
        flex-direction: column;

        button {
          flex: none;
        }
      }
    }
  }

  .tech-advantages {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }
}
</style>
