// 产品展示区域
.products-showcase {
  @include section-padding(var(--space-20), var(--space-16));
  
  .showcase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-16);
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--space-4);
      text-align: center;
    }
    
    .showcase-title {
      font-size: var(--text-3xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      @include gradient-text(var(--gradient-primary));
    }
    
    .showcase-meta {
      display: flex;
      align-items: center;
      gap: var(--space-6);
      
      .product-count {
        font-size: var(--text-sm);
        color: var(--gray-600);
        font-weight: var(--font-medium);
      }
      
      .view-toggle {
        display: flex;
        background: var(--gray-100);
        border-radius: var(--radius-lg);
        padding: var(--space-1);
        
        .toggle-btn {
          padding: var(--space-2) var(--space-3);
          background: transparent;
          border: none;
          border-radius: var(--radius-md);
          color: var(--gray-600);
          cursor: pointer;
          transition: var(--transition-all);
          
          &.active {
            background: var(--white);
            color: var(--primary-blue);
            box-shadow: var(--shadow-sm);
          }
        }
      }
    }
  }
  
  .products-grid {
    display: grid;
    gap: var(--space-8);
    
    &.grid {
      grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }
    
    &.list {
      grid-template-columns: 1fr;
      
      .product-card {
        display: grid;
        grid-template-columns: auto 1fr auto;
        align-items: center;
        gap: var(--space-6);
        
        .card-visual {
          width: 120px;
          height: 120px;
        }
        
        .card-footer {
          flex-direction: column;
          align-items: flex-end;
        }
      }
    }
  }
  
  .product-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition-all);
    
    @include card-hover(1.02, -6px);
    
    .card-visual {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;
      height: 160px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      
      .product-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
        z-index: 3;
        position: relative;
        transition: var(--transition-all);
      }
      
      .product-badge {
        position: absolute;
        top: var(--space-4);
        right: var(--space-4);
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-2xl);
        font-size: var(--text-xs);
        font-weight: var(--font-bold);
        color: var(--white);
        z-index: 4;
        
        &.high {
          background: var(--gradient-primary);
        }
        
        &.urgent {
          background: var(--gradient-innovation);
        }
        
        &.medium {
          background: var(--warning-orange);
        }
      }
      
      .card-gradient {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-glass);
        opacity: 0.1;
        z-index: 1;
      }
    }
    
    &:hover {
      .card-visual .product-icon {
        transform: scale(1.1) rotateY(15deg);
      }
    }
    
    .card-content {
      padding: var(--space-6);
      
      .product-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--space-4);
        
        .product-title {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--gray-900);
          line-height: var(--leading-tight);
          flex: 1;
          margin-right: var(--space-3);
        }
        
        .product-status {
          display: flex;
          align-items: center;
          gap: var(--space-1);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-lg);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
          white-space: nowrap;
          
          .status-icon {
            font-size: var(--text-sm);
          }
          
          &.运行中 {
            background: var(--success-green);
            color: var(--white);
          }
          
          &.最新版 {
            background: var(--gradient-innovation);
            color: var(--white);
          }
          
          &.稳定版 {
            background: var(--primary-blue);
            color: var(--white);
          }
          
          &.推广中 {
            background: var(--warning-orange);
            color: var(--white);
          }
        }
      }
      
      .product-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
        font-size: var(--text-sm);
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .product-features {
        margin-bottom: var(--space-4);
        
        .features-title {
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }
        
        .features-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
          
          .feature {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);
            
            .feature-icon {
              color: var(--success-green);
              font-size: var(--text-base);
              flex-shrink: 0;
            }
          }
          
          .feature-more {
            font-size: var(--text-xs);
            color: var(--primary-blue);
            font-weight: var(--font-medium);
            padding-left: var(--space-6);
          }
        }
      }
      
      .product-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);
        margin-bottom: var(--space-4);
        
        .tag {
          background: var(--crystal-blue);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }
        
        .tag-more {
          background: var(--gray-100);
          color: var(--gray-600);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }
      }
    }
    
    .card-footer {
      padding: var(--space-4) var(--space-6);
      border-top: 1px solid var(--gray-100);
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .product-metrics {
        display: flex;
        gap: var(--space-4);
        
        .metric {
          display: flex;
          align-items: center;
          gap: var(--space-1);
          font-size: var(--text-xs);
          color: var(--gray-600);
        }
      }
      
      .detail-btn {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: transparent;
        color: var(--primary-blue);
        border: 1px solid var(--primary-blue);
        border-radius: var(--radius-lg);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);
        
        .btn-arrow {
          font-size: var(--text-sm);
          transition: var(--transition-all);
        }
        
        &:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: translateY(-1px);
          box-shadow: var(--shadow-md);
          
          .btn-arrow {
            transform: translateX(2px);
          }
        }
      }
    }
  }
  
  // 空状态
  .empty-state {
    text-align: center;
    padding: var(--space-20) var(--space-8);
    
    .empty-visual {
      position: relative;
      margin-bottom: var(--space-8);
      
      .empty-icon {
        font-size: 80px;
        color: var(--gray-300);
        margin-bottom: var(--space-4);
      }
      
      .empty-animation {
        display: flex;
        justify-content: center;
        gap: var(--space-2);
        
        .animation-dot {
          width: 8px;
          height: 8px;
          background: var(--primary-blue);
          border-radius: 50%;
          animation: bounce 1.4s infinite ease-in-out both;
          
          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) { animation-delay: 0s; }
        }
      }
    }
    
    .empty-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-700);
      margin-bottom: var(--space-3);
    }
    
    .empty-description {
      font-size: var(--text-base);
      color: var(--gray-600);
      margin-bottom: var(--space-6);
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .empty-action {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-6);
      background: var(--gradient-primary);
      color: var(--white);
      border: none;
      border-radius: var(--radius-2xl);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: var(--transition-all);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
      }
    }
  }
}

@keyframes bounce {
  0%, 80%, 100% { 
    transform: scale(0);
  } 40% { 
    transform: scale(1.0);
  }
}

// 产品优势区域
.advantages-section {
  @include section-padding(var(--space-20), var(--space-16));
  background: var(--gradient-subtle);
  
  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);
    
    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-10);
      color: var(--primary-blue);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      margin-bottom: var(--space-6);
    }
    
    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }
    
    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }
  
  .advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .advantage-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;
    
    @include card-hover(1.03, -6px);
    
    .advantage-visual {
      display: flex;
      align-items: center;
      gap: var(--space-4);
      margin-bottom: var(--space-6);
      position: relative;
      
      .advantage-number {
        font-size: var(--text-4xl);
        font-weight: var(--font-black);
        color: var(--primary-alpha-20);
        line-height: 1;
        user-select: none;
      }
      
      .advantage-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        box-shadow: var(--glow-primary);
        z-index: 2;
        position: relative;
      }
      
      .visual-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 80px;
        height: 80px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: 1;
      }
    }
    
    .advantage-content {
      .advantage-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
      }
      
      .advantage-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
      }
      
      .advantage-details {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        
        .detail {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: var(--gray-700);
          
          .detail-icon {
            color: var(--primary-blue);
            font-size: var(--text-base);
            flex-shrink: 0;
          }
        }
      }
    }
    
    .advantage-decorator {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 60px;
      height: 60px;
      background: var(--gradient-primary);
      border-radius: 50% 0 var(--radius-2xl) 0;
      opacity: 0.1;
    }
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-24), var(--space-24));
  overflow: hidden;
  
  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }
  
  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--midnight-blue) 0%,
      var(--corporate-blue) 50%,
      var(--primary-blue) 100%
    );
  }
  
  .cta-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    .pattern-dot {
      position: absolute;
      width: 2px;
      height: 2px;
      background: var(--white);
      border-radius: 50%;
      opacity: 0.1;
      
      @for $i from 1 through 50 {
        &:nth-child(#{$i}) {
          top: #{random(100)}%;
          left: #{random(100)}%;
        }
      }
    }
  }
  
  .cta-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-16);
    align-items: center;
    
    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-12);
    }
  }
  
  .cta-text {
    .cta-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--white);
      color: var(--primary-blue);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      margin-bottom: var(--space-6);
    }
    
    .cta-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      margin-bottom: var(--space-6);
      line-height: var(--leading-tight);
      
      @media (max-width: 768px) {
        font-size: var(--text-3xl);
      }
    }
    
    .cta-subtitle {
      font-size: var(--text-lg);
      line-height: var(--leading-relaxed);
      opacity: 0.9;
      margin-bottom: var(--space-8);
      
      strong {
        color: var(--electric-blue);
      }
    }
    
    .cta-features {
      display: flex;
      gap: var(--space-6);
      
      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--space-4);
      }
      
      .cta-feature {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
        opacity: 0.8;
      }
    }
  }
  
  .cta-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: flex-end;
    
    @media (max-width: 968px) {
      align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-4) var(--space-6);
      border-radius: var(--radius-2xl);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: var(--transition-all);
      border: none;
      white-space: nowrap;
      
      &.large {
        padding: var(--space-5) var(--space-8);
        font-size: var(--text-lg);
      }
      
      .btn-arrow {
        transition: var(--transition-all);
      }
      
      &:hover .btn-arrow {
        transform: translateX(4px);
      }
    }
    
    .btn-primary {
      background: var(--white);
      color: var(--primary-blue);
      box-shadow: var(--shadow-lg);
      
      &:hover {
        background: var(--crystal-blue);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
      }
    }
    
    .btn-secondary {
      background: transparent;
      color: var(--white);
      border: 2px solid var(--white);
      @include glass-morphism(0.1, 12px, 0.3);
      
      &:hover {
        background: var(--white);
        color: var(--primary-blue);
        transform: translateY(-2px);
      }
    }
    
    .contact-info {
      display: flex;
      gap: var(--space-6);
      margin-top: var(--space-4);
      
      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--space-2);
        text-align: center;
      }
      
      .contact-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
        opacity: 0.8;
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section .container {
    .hero-content,
    .hero-visual {
      grid-column: 1;
    }
    
    .hero-visual {
      order: -1;
    }
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: var(--text-4xl) !important;
  }
  
  .hero-subtitle {
    font-size: var(--text-lg);
  }
  
  .categories-section {
    .categories-grid {
      grid-template-columns: 1fr;
    }
    
    .category-card {
      flex-direction: column;
      text-align: center;
      gap: var(--space-3);
    }
  }
  
  .products-showcase {
    .showcase-header {
      .showcase-title {
        font-size: var(--text-2xl);
      }
    }
    
    .products-grid.list .product-card {
      grid-template-columns: 1fr;
      text-align: center;
      
      .card-footer {
        flex-direction: column;
        align-items: center;
        gap: var(--space-3);
      }
    }
  }
  
  .advantages-section {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
    
    .advantages-grid {
      grid-template-columns: 1fr;
    }
  }
}